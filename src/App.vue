<script setup lang="ts">


import {onMounted} from "vue";
import {useCountStore} from "./stores/count.ts";
import {storeToRefs} from "pinia";

// defineStore()返回的函数调用一次
const countStore = useCountStore()

// 解构 ECMAScript 6
// 丢失响应式
// const {count, doubleCount} = countStore

// 不会丢失响应式
const {count, doubleCount} = storeToRefs(countStore)

const handleAdd = () => {
  countStore.addCount()
}

onMounted(() => {
  countStore.getChannelList()
})

</script>

<template>
<!--  <div>
    <p>{{ countStore.count }}</p>
    <p>{{ countStore.doubelCount }}</p>
    <button @click="handleAdd">加一</button>

    <ol>
      <li v-for="channel in countStore.channelRes?.data.channels" :key="channel.id">
        {{ channel.name }}
      </li>
    </ol>
  </div>-->


  <div>
    <p>{{ count }}</p>
    <p>{{ doubleCount }}</p>
    <button @click="handleAdd">加一</button>

    <ol>
      <li v-for="channel in countStore.channelRes?.data.channels" :key="channel.id">
        {{ channel.name }}
      </li>
    </ol>
  </div>

</template>

<style scoped>
</style>
